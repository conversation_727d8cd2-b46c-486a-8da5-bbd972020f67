<?php
/**
 * BACS payment method - Customer details.
 *
 * @package WooCommerce\Templates
 * @version 8.6.0
 */

defined( 'ABSPATH' ) || exit;

if ( ! empty( $account_details ) ) {
	echo '<section class="woocommerce-bacs-bank-details">';

	if ( ! empty( $description ) ) {
		echo '<h2 class="wc-bacs-bank-details-heading">' . wp_kses_post( $description ) . '</h2>';
	}

	echo '<ul class="wc-bacs-bank-details order_details bacs_details">';

	// Isim bolumunu en basa ekle
	echo '<li class="bank_name"><strong>' . esc_html__( 'İsim:', 'woocommerce' ) . '</strong> Akademir</li>';

	foreach ( $account_details as $account ) {
		$bank_name   = ! empty( $account['bank_name'] ) ? $account['bank_name'] : '';
		$account_name = ! empty( $account['account_name'] ) ? $account['account_name'] : '';
		$account_number = ! empty( $account['account_number'] ) ? $account['account_number'] : '';
		$sort_code   = ! empty( $account['sort_code'] ) ? $account['sort_code'] : '';
		$iban        = ! empty( $account['iban'] ) ? $account['iban'] : '';
		$bic         = ! empty( $account['bic'] ) ? $account['bic'] : '';

		if ( $bank_name ) {
			echo '<li class="bank_name"><strong>' . esc_html__( 'Banka:', 'woocommerce' ) . '</strong> ' . wp_kses_post( wptexturize( $bank_name ) ) . '</li>';
		}

		if ( $account_name ) {
			echo '<li class="account_name"><strong>' . esc_html__( 'Hesap sahibi:', 'woocommerce' ) . '</strong> ' . wp_kses_post( wptexturize( $account_name ) ) . '</li>';
		}

		if ( $account_number ) {
			echo '<li class="account_number"><strong>' . esc_html__( 'Hesap numarası:', 'woocommerce' ) . '</strong> ' . wp_kses_post( wptexturize( $account_number ) ) . '</li>';
		}

		if ( $sort_code ) {
			echo '<li class="sort_code"><strong>' . esc_html__( 'Şube kodu:', 'woocommerce' ) . '</strong> ' . wp_kses_post( wptexturize( $sort_code ) ) . '</li>';
		}

		if ( $iban ) {
			echo '<li class="iban"><strong>' . esc_html__( 'IBAN:', 'woocommerce' ) . '</strong> ' . wp_kses_post( wptexturize( $iban ) ) . '</li>';
		}

		if ( $bic ) {
			echo '<li class="bic"><strong>' . esc_html__( 'BIC:', 'woocommerce' ) . '</strong> ' . wp_kses_post( wptexturize( $bic ) ) . '</li>';
		}
	}

	echo '</ul>';
	echo '</section>';
}
?>
